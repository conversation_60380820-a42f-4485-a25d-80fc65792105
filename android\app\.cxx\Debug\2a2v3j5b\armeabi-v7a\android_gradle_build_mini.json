{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Work\\Studio Projects\\chat_app_frontend\\android\\app\\.cxx\\Debug\\2a2v3j5b\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Work\\Studio Projects\\chat_app_frontend\\android\\app\\.cxx\\Debug\\2a2v3j5b\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}